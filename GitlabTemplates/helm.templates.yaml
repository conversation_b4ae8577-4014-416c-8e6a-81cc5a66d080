.deploy_helm_template:
  tags:
    - docker
  script:
    - |
      # Определяем тег в зависимости от ветки
      if [ "$CI_COMMIT_REF_NAME" = "develop" ]; then
        TAG="$CI_COMMIT_SHORT_SHA";
      elif [[ "$CI_COMMIT_REF_NAME" =~ ^(release|hotfix)/ ]]; then
        TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      else
        TAG="$CI_COMMIT_REF_NAME";
      fi
      
      cat ${KUBECONFIG_DEVELOP} > kubeconfig.yaml
      export KUBECONFIG=kubeconfig.yaml
      
      # Базовые параметры Helm
      HELM_CMD="helm upgrade ${SERVICE_NAME} ${CHART_PATH} \
        --install \
        --namespace ${KUBE_NAMESPACE} \
        --set image.repository=${DOCKER_REPOSITORY_ADDR}/${IMAGE_PATH}/${SERVICE_NAME} \
        --set image.tag=${TAG} \
        -f ${CHART_PATH}/values.yaml \
        --values ${CHART_PATH}/values.${STAGE}.yaml \
        --debug"
      
      # Добавляем дополнительные параметры если они есть
      if [ -n "${EXTRA_HELM_ARGS}" ]; then
        HELM_CMD="${HELM_CMD} ${EXTRA_HELM_ARGS}"
      fi
      
      # Выполняем команду
      eval "${HELM_CMD} --wait"