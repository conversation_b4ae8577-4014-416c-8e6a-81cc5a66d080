package ru.sbertroika.tms.gate.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.r2dbc.repository.R2dbcRepository
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.data.repository.query.Param
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.tms.gate.model.TerminalListFilter
import ru.sbertroika.tms.gate.output.model.db.*
import ru.sbertroika.tms.model.db.Terminal
import ru.sbertroika.tms.model.db.TerminalPK
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*


@Repository
open class TerminalRepository(
    @Qualifier("mainDatabaseClient")
    private val dbClient: DatabaseClient,
    private val crudRepo: TerminalCrudRepo
) {

    private fun getQuery(isCount: Boolean, filter: TerminalListFilter): String {
        var query = (if (isCount) "SELECT COUNT(*) " else "SELECT * ") + "FROM terminal_view as t "

        var isWhereAdded = false
        if (filter.organizationId.isNotEmpty()) {
            query += "WHERE t.org_id = '${filter.organizationId}'\n"
            isWhereAdded = true
        }
        if (filter.serialNumber.isNotEmpty()) {
            query += (if (isWhereAdded) " AND " else " WHERE ") + "t.t_serial_number = '${filter.serialNumber}'"
            isWhereAdded = true
        }
        if (filter.status != TerminalStatus.ALL) {
            query += (if (isWhereAdded) " AND " else " WHERE ") + "t.t_status = ${filter.status.ordinal}"
            // isWhereAdded = true
        }
        return query
    }

    suspend fun searchTerminals(filter: TerminalListFilter, page: Long, limit: Int): Flow<Terminal> {
        var query = getQuery(false, filter)
        query += " ORDER BY t.t_serial_number\n" +
                "OFFSET ${page * limit} \n" +
                "LIMIT $limit"
        return dbClient.sql(query).map(::toTerminal).flow()
    }

    suspend fun countTerminals(filter: TerminalListFilter): Int {
        val query = getQuery(true, filter)
        return dbClient.sql(query).map { t ->
            (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    suspend fun findMaxIndex(projectId: String): Int? {
        val query = "SELECT MAX(t.t_index) as max FROM terminal t\n" +
                "   INNER JOIN (SELECT t_id, MAX(t_version) vers FROM terminal GROUP BY t_id) t2\n" +
                "      ON t.t_id = t2.t_id AND t.t_version = t2.vers AND t.t_id IN (\n" +
                "        SELECT torg.to_terminal_id FROM terminal_organization torg\n" +
                "            WHERE torg.to_organization_id IN (\n" +
                "                SELECT po2.po_organization_id FROM project_organization po2 WHERE po2.po_project_id = '$projectId' \n" +
                "                                AND po2.po_active_till IS NULL)\n" +
                "        AND  torg.to_active_till is null)"
        return dbClient.sql(query).map { t ->
            val result  = t.get("max") as Int?
            result ?: 0
        }.awaitOneOrNull()
    }

    suspend fun save(org: Terminal) = crudRepo.save(org)

    suspend fun findBySerialNumber(sn: String): Terminal? = crudRepo.findBySerialNumber(sn)
    suspend fun findById(id: String): Terminal? {
        val query = "SELECT * FROM terminal_view as t WHERE t_id = '$id'"
        return dbClient.sql(query).map(::toTerminal).awaitOneOrNull()
    }

    private fun toTerminal(t: io.r2dbc.spi.Readable) = Terminal(
        tId = t.get("t_id") as UUID,
        tSerialNumber = t.get("t_serial_number") as String,
        tTitle = t.get("t_title") as String,
        tTypeId = t.get("t_type_id") as UUID,
        tActiveFrom = Timestamp.valueOf(t.get("t_active_from") as LocalDateTime),
        tActiveTill = if (t.get("t_active_till") == null) null else Timestamp.valueOf(t.get("t_active_till") as LocalDateTime),
        tStatus = t.get("t_status") as Int,
        orgId = t.get("org_id") as UUID,
        tBluetoothMac = t.get("t_bluetooth_mac") as String?,
        tEthernetMac = t.get("t_ethernet_mac") as String?,
        tWifiMac = t.get("t_wifi_mac") as String?,
        tVersionPO = t.get("t_version_po") as String?,
        tVersion = t.get("t_version") as Int,
        tVersionCreatedAt = if (t.get("t_version_created_at") == null) null else Timestamp.valueOf(t.get("t_version_created_at") as LocalDateTime),
        tVersionCreatedBy = t.get("t_version_created_by") as UUID?,
        tActivated = t.get("t_activated") as Boolean,
        tTid = t.get("t_tid") as String?
    )
}

interface TerminalCrudRepo : R2dbcRepository<Terminal, TerminalPK> {
    @Query(
        "SELECT distinct on (t_serial_number) t_id, t_version, t_title, t_status, t_version_po, t_serial_number, t_tid,\n " +
                " t_version_po, t_type_id, t_active_from, t_active_till, t_wifi_mac, \n" +
                " t_bluetooth_mac, t_ethernet_mac, t_activated, t_version_created_at, t_version_created_by, t_index, tp_id \n" +
                "FROM terminal WHERE t_serial_number = :sn \n " +
                    "ORDER BY t_serial_number, t_version DESC"
    )
    suspend fun findBySerialNumber(@Param("sn") sn: String): Terminal?
}


interface TerminalConnectionsRepo : CoroutineCrudRepository<TerminalConnections, TerminalConnectionsPK>

interface ProjectRepo : CoroutineCrudRepository<Project, UUID>

interface ProjectOrganizationRepo : CoroutineCrudRepository<ProjectOrganization, UUID> {

    @Query(value = "select * from project_organization_view o WHERE o.po_organization_id = :organizationId AND o.po_project_id = :projectId and o.po_active_till is null LIMIT 1")
    suspend fun getAllByOrganizationIdAndProjectId(@Param("organizationId")organizationId: UUID, @Param("projectId")projectId: UUID): Flow<ProjectOrganization>

}

interface ProjectFunctionRepo : CoroutineCrudRepository<ProjectFunction, UUID>
