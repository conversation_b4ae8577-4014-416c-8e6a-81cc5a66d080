# Default values for tms-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: tms-processing

replicaCount: 1

image:
  repository: tms-processing
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi

env:
  client:
    logging:
      enable: true
  kafka:
    servers: "10.4.32.25:9092;10.4.32.140:9092;10.4.32.88:9092"
  zookeeper:
    nodes: "10.4.45.220:2181,10.4.45.19:2181,10.4.45.163:2181"
