package ru.sbertroika.tms.model.db

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

data class TerminalPK(
    val tId: UUID? = null,
    val tVersion:Int? = null,
): Serializable

@Table("terminal")
data class Terminal(

    @Column("t_id")
    var tId: UUID? = null,

    @Column("t_version")
    var tVersion: Int? = null,

    @Column("t_version_created_at")
    var tVersionCreatedAt: Timestamp? = null,

    @Column("t_version_created_by")
    var tVersionCreatedBy: UUID? = null,

    @Column("t_serial_number")
    var tSerialNumber: String? = null,

    @Column("t_title")
    var tTitle: String? = null,

    /**
     * @see TerminalStatus
     */
    @Column("t_status")
    var tStatus: Int? = null,

    @Column("t_version_po")
    var tVersionPO: String? = null,

    @Column("t_type_id")
    var tTypeId: UUID? = null,

    @Column("t_active_from")
    var tActiveFrom: Timestamp? = null,

    @Column("t_active_till")
    var tActiveTill: Timestamp? = null,

    @Column("t_wifi_mac")
    var tWifiMac: String? = null,

    @Column("t_bluetooth_mac")
    var tBluetoothMac: String? = null,

    @Column("t_ethernet_mac")
    var tEthernetMac: String? = null,

    @Column("t_activated")
    var tActivated: Boolean? = null,

    @Column("t_tid")
    var tTid: String? = null,

    @Column("tp_id")
    var tpId: UUID? = null,

    @Column("t_index")
    var index: Int? = null,

    // the field is transient (not in a db)
    var orgId: UUID? = null
)