# GitLab CI/CD Pipeline для tms-domain (shell executor + docker run)

include:
  - local: '/GitlabTemplates/helm.templates.yaml'
  - local: '/GitlabTemplates/security.template.yaml'

variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"
  DOCKER_BASE_IMAGE: "gradle:8.5-jdk17"
  KUBE_NAMESPACE: "tms"

cache:
  key: "$CI_COMMIT_REF_SLUG"
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - .gradle/daemon

stages:
  - build
  - test
  - publish-snapshot
  - tms-gate-deploy-dev
  - tms-processing-deploy-dev
  - deploy
  - release
#  - security

.docker_gradle: &docker_gradle
  tags:
    - docker
  before_script:
    - echo "Docker base image $DOCKER_BASE_IMAGE"
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh
#  after_script:
#    - chmod -R 777 "$PWD/.gradle" || true
#    - chmod -R 777 "$PWD/build" || true


# ========================
# 1. ЛЮБАЯ ВЕТКА (кроме develop/master): build + test
# ========================

build_and_test:
  <<: *docker_gradle
  stage: test
  script:
    - docker run --rm -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -u `id -u $USER`
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
      ./scripts/setup-gradle-auth.sh
      gradle build --no-daemon
      gradle test --no-daemon
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME != "develop" && $CI_COMMIT_REF_NAME != "master" && $CI_COMMIT_TAG == null

# ========================
# 2. DEVELOP: build + test + publish SNAPSHOT (одним шагом)
# ========================

develop_build_test_publish:
  <<: *docker_gradle
  stage: publish-snapshot
  script:
    - docker run --rm -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -u `id -u $USER`
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
      ./scripts/setup-gradle-auth.sh
      gradle build --no-daemon
      gradle test --no-daemon
      gradle publishToNexusSnapshot --no-daemon
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"
  environment:
    name: develop
    url: https://nexus.sbertroika.tech/repository/maven-snapshots/

# ========================
# 3. MASTER: только release (без отдельного build/test)
# ========================

release:
  <<: *docker_gradle
  stage: release
  before_script:
    - |
      if echo "$CI_COMMIT_MESSAGE" | grep -q "^\[Gradle Release Plugin\]"; then
        echo "Gradle Release Plugin commit detected, skipping job."
        exit 0
      fi
    - export DOCKER_GRADLE_CACHE="$PWD/.gradle"
    - mkdir -p "$DOCKER_GRADLE_CACHE"
    - chmod -R 777 "$DOCKER_GRADLE_CACHE" || true
    - chmod +x scripts/setup-gradle-auth.sh
    - chown -R $(id -u):$(id -g) .git/ || true
    - chmod -R 755 .git/ || true
    - rm -f .git/index.lock
    - eval $(ssh-agent -s)
    - ssh-add ~/.ssh/id_rsa || echo "SSH key not found, trying alternative locations"
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H git.sbertroika.ru >> ~/.ssh/known_hosts
  script:
    - echo "Выпуск релиза версии"
    - >
      docker run --rm
      -v "$PWD":/workspace
      -v "$PWD/.gradle":/tmp/.gradle
      -v "$SSH_AUTH_SOCK":/tmp/ssh_agent.sock
      -e SSH_AUTH_SOCK=/tmp/ssh_agent.sock
      -e MAVEN_USER="$MAVEN_USER"
      -e MAVEN_PASSWORD="$MAVEN_PASSWORD"
      -e GRADLE_USER_HOME="/tmp/.gradle"
      -w /workspace $DOCKER_BASE_IMAGE bash -c "
        mkdir -p /root/.ssh
        chmod 700 /root/.ssh
        ssh-keyscan -H git.sbertroika.ru >> /root/.ssh/known_hosts
        mkdir -p /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        ssh-keyscan -H git.sbertroika.ru >> /home/<USER>/.ssh/known_hosts
        export GIT_SSH_COMMAND='ssh -o UserKnownHostsFile=/root/.ssh/known_hosts'
        ./scripts/setup-gradle-auth.sh
        git config --global --add safe.directory /workspace
        git config --global user.name 'GitLab CI'
        git config --global user.email '<EMAIL>'
        git remote set-<NAME_EMAIL>:tkp3/tms/tms-domain.git
        git fetch origin master
        git checkout -B master origin/master
        gradle release --no-daemon --info
        chmod -R 777 /workspace
        chmod -R 777 /tmp/.gradle
      "
  artifacts:
    paths:
      - build/
      - "**/build/reports/"
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_REF_NAME == "master" && $CI_COMMIT_MESSAGE !~ /^\[Gradle Release Plugin\]/
    - if: '$CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never  # Skip during MR creation/update
    - if: '$CI_COMMIT_REF_NAME == "master" && $CI_PIPELINE_SOURCE == "push"'
      when: on_success  # Run only after merge (push to master)
  environment:
    name: production
    url: https://nexus.sbertroika.tech/repository/maven-releases/

# ========================
# TMS-GATE: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
tms_gate_build_develop:
  stage: tms-gate-deploy-dev
  needs: [develop_build_test_publish]
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_SHORT_SHA"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/tms-gate:$TAG --pull -f tms-gate/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/tms-gate:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-gate/**/*

tms_gate_deploy_chart_develop:
  stage: tms-gate-deploy-dev
  needs:
    - tms_gate_build_develop
  tags:
    - docker
  variables:
    SERVICE_NAME: "tms-gate"
    CHART_PATH: "./charts/tms-gate"
    IMAGE_PATH: "tkp3"
    STAGE: "dev"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-gate/**/*

# --- TAG ---
tms_gate_build_tag:
  stage: build
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_TAG"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/tms-gate:$TAG --pull -f tms-gate/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/tms-gate:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - tms-gate/**

tms_gate_deploy_chart_tag:
  stage: deploy
  tags:
    - docker
  script:
    - SERVICE_NAME="tms-gate"
    - TAG="$CI_COMMIT_TAG"
    - mkdir .kube
    - cat $KUBECONFIG_DEVELOP > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $SERVICE_NAME /apps/charts/$SERVICE_NAME
      --install
      --namespace $KUBE_NAMESPACE       
      --set namespace=$KUBE_NAMESPACE
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$SERVICE_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - tms-gate/**
  needs: [tms_gate_build_tag]

# ========================
# TMS-PROCESSING: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
tms_processing_build_develop:
  stage: tms-processing-deploy-dev
  needs: [develop_build_test_publish]
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_SHORT_SHA"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$TAG --pull -f tms-processing/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$TAG
#    - docker save -o tms-processing-image.tar $DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-processing/**/*
#  artifacts:
#    paths:
#      - tms-processing-image.tar  # 👈 Pass the saved image to next job
#    expire_in: 1 hour  # Short expiry since it's large


tms_processing_deploy_chart_develop:
  stage: tms-processing-deploy-dev
  needs:
    - job: tms_processing_build_develop
      optional: true
  variables:
    SERVICE_NAME: "tms-processing"
    CHART_PATH: "./charts/tms-processing"
    IMAGE_PATH: "tkp3"
    STAGE: "dev"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-processing/**/*
        - charts/tms-processing/**/*

# --- TAG ---
tms_processing_build_tag:
  stage: build
  tags:
    - docker
  script:
    - export MAVEN_USER="$MAVEN_USER"
    - export MAVEN_PASSWORD="$MAVEN_PASSWORD"
    - scripts/setup-gradle-docker.sh
    - TAG="$CI_COMMIT_TAG"
    - docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle -t $DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$TAG --pull -f tms-processing/Dockerfile .
    - docker push $DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$TAG
    - rm -rf .ci-gradle
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - tms-processing/**/*

tms_processing_deploy_chart_tag:
  stage: deploy
  needs:
    - job: tms_processing_build_tag
  tags:
    - docker
  script:
    - SERVICE_NAME="tms-processing"
    - TAG="$CI_COMMIT_TAG"
    - mkdir .kube
    - cat $KUBECONFIG_DEVELOP > .kube/config
    - docker run
      -u $(id -u):$(id -g)
      -v $(pwd)/:/apps
      -v $(pwd)/.kube:/.kube
      -w /apps
      -i alpine/helm upgrade $SERVICE_NAME /apps/charts/$SERVICE_NAME
      --install
      --namespace $KUBE_NAMESPACE
      --set namespace=$KUBE_NAMESPACE
      --set image.repository=$DOCKER_REPOSITORY_ADDR/tkp3/$SERVICE_NAME
      --set image.tag=$TAG
      --set image.pullPolicy=Always
      --wait
      --kubeconfig=/.kube/config
    - rm -rf .kube
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - tms-processing/**/*

#
#  # Security scans for each image
#scan-tms-gate-dev:
#  extends: .trivy-container-scan
#  stage: security
#  needs:
#    - job: tms_gate_build_develop
#      artifacts: true
#  variables:
#    IMAGE_ALIAS: "tms-gate"
##    IMAGE_TO_SCAN: "$DOCKER_REPOSITORY_ADDR/tkp3/tms-gate:$CI_COMMIT_TAG"
#    IMAGE_TO_SCAN: "tms-gate-image:local"
#  before_script:
#    - docker load -i tms-gate-image.tar  # 👈 Load the image
#    - docker tag $DOCKER_REPOSITORY_ADDR/tkp3/tms-gate:$CI_COMMIT_SHORT_SHA tms-gate-image:local
#  rules:
#    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
#      changes:
#        - tms-gate/**/*
#
#scan-tms-processing-dev:
#  extends: .trivy-container-scan
#  stage: security
#  needs:
#    - job: tms_processing_build_develop
#      artifacts: true
#  variables:
#    IMAGE_ALIAS: "tms-processing"
##    IMAGE_TO_SCAN: "$DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$CI_COMMIT_TAG"
#    IMAGE_TO_SCAN: "tms-processing-image:local"
#  before_script:
#    - docker load -i tms-gate-image.tar  # 👈 Load the image
#    - docker tag $DOCKER_REPOSITORY_ADDR/tkp3/tms-processing:$CI_COMMIT_SHORT_SHA tms-processing-image:local
#  rules:
#    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
#      changes:
#        - tms-processing/**/*
