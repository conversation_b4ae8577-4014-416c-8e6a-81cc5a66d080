package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import org.springframework.stereotype.Service
import ru.sbertroika.common.NotFound
import ru.sbertroika.common.toTimestamp
import ru.sbertroika.common.v1.*
import ru.sbertroika.tkp3.manifest.starter.ManifestService
import ru.sbertroika.tms.gate.config.ManifestNotFound
import ru.sbertroika.tms.gate.config.ManifestStateError
import ru.sbertroika.tms.gate.config.TerminalNotProject
import ru.sbertroika.tms.gate.output.DicService
import ru.sbertroika.tms.gate.output.repository.TerminalRepository
import ru.sbertroika.tms.gate.output.repository.TerminalToOrganizationRepository
import ru.sbertroika.tms.gate.v1.*
import ru.sbertroika.tms.model.db.TerminalOrganization

@Service
class DicServiceImpl(
    private val manifestService: ManifestService,
    private val terminalRepository: TerminalRepository,
    private val terminalToOrganizationRepository: TerminalToOrganizationRepository
) : DicService {

    override suspend fun getManifest(terminalSerial: String): Either<Throwable, Manifest> = Either.catch {
        getTerminalManifest(terminalSerial).fold(
            {
                throw it
            },
            { res ->
                if (res.third.service.pasiv == null) throw ManifestStateError("Отсутсвуют данные справочников")
                if (res.third.service.pasiv!!.dict.organization.isEmpty()) throw ManifestStateError("Отсутсвуюте данные об организации")
                val org = res.third.service.pasiv!!.dict.organization.find { it.id == res.second.toOrganizationId } ?: throw ManifestStateError("Отсутсвуюте данные об организации")

                manifest {
                    id = res.third.id
                    version = res.third.version
                    validFrom = res.third.validFrom.toTimestamp()
                    validTill = res.third.validTill.toTimestamp()
                    projectIndex = res.third.projectIndex
                    organization = Manifest.Organization.newBuilder()
                        .setId(org.id.toString())
                        .setName(org.name)
                        .setInn(org.inn)
                        .setAddress(org.address)
                        .setPaymentPlace(org.paymentPlace)
                        .build()
                }
            }
        )
    }

    override suspend fun getStationList(manifest: Manifest): Either<Throwable, StationListResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                stationListResult {
                    station += res.service.pro!!.dict.station.map { s ->
                        station {
                            id = s.id.toString()
                            name = s.name
                            lat = s.lat
                            lon = s.lon
                        }
                    }.toList()
                }
            }
        )
    }

    override suspend fun getTariffList(manifest: Manifest): Either<Throwable, TariffListResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                tariffListResult {
                    tariff += res.service.pro!!.dict.tariff.map { s ->
                        tariff {
                            id = s.id.toString()
                            name = s.name
                            constraint += s.constraint.map { toConstraint(it) }.toList()
                        }
                    }.toList()
                }
            }
        )
    }

    override suspend fun getRouteList(manifest: Manifest): Either<Throwable, RouteListResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                routeListResult {
                    route += res.service.pro!!.dict.route.map { r ->
                        route {
                            id = r.id.toString()
                            name = r.name
                            scheme = RouteScheme.valueOf(r.scheme.name)
                            station += r.station.map { rs ->
                                routeStation {
                                    id = rs.id.toString()
                                    pos = rs.pos
                                }
                            }.toList()
                            constraint += r.constraint.map { toConstraint(it) }.toList()
                            routeIndex = r.routeIndex
                        }
                    }.toList()
                }
            }
        )
    }

    override suspend fun getProductList(manifest: Manifest): Either<Throwable, ProductListResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                productListResult {
                    product += res.service.pro!!.dict.product.map { p ->
                        product {
                            id = p.id.toString()
                            name = p.name
                        }
                    }.toList()
                }
            }
        )
    }

    override suspend fun getTransportList(manifest: Manifest): Either<Throwable, TransportListResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                transportListResult {
                    transport += res.service.pro!!.dict.transport.map { t ->
                        transport {
                            id = t.id.toString()
                            number = t.number
                            type = TransportType.valueOf(t.type.name)
                            constraint += t.constraint.map { toConstraint(it) }.toList()
                        }
                    }.toList()
                }
            }
        )
    }

    override suspend fun getProductMenu(manifest: Manifest): Either<Throwable, ProductMenuResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                productMenuResult {
                    menu += res.service.pro!!.dict.menu.map { m ->
                        productMenu {
                            productId = m.productId.toString()
                            tariffId = m.tariffId.toString()
                            priceRules += m.priceRules.map { r ->
                                priceRule {
                                    paymentType = PriceRule.TPaymentType.valueOf(r.paymentType.name)
                                    price = r.price
                                    matrix += r.matrix.map { item ->
                                        priceRuleMatrixItem {
                                            stationFrom = item.stationFrom.toString()
                                            stationTo = item.stationTo.toString()
                                            price = item.price
                                        }
                                    }.toList()
                                }
                            }.toList()
                        }
                    }.toList()
                }
            }
        )
    }

    override suspend fun getSubscriptionSettings(manifest: Manifest): Either<Throwable, SubscriptionSettingsResult> = Either.catch {
        getManifest(manifest).fold(
            {
                throw it
            },
            { res ->
                subscriptionSettingsResult {
                    if (res.service.proAbt?.dict?.templates != null) {
                        abtTemplate += res.service.proAbt?.dict?.templates!!.map { template ->
                            abtTemplate {
                                id = template.id.toString()
                                appCode = template.appCode
                                crdCode = template.crdCode
                                name = template.name
                                type = AbonementType.valueOf(template.type.name)
                                rules += template.rules.map { rule ->
                                    abtTemplatePassRule {
                                        index = rule.index
                                        action = rule.action
                                    }
                                }.toList()
                                counter += template.counters.map { counter ->
                                    abtTemplateCounter {
                                        type = SubscriptionCounterType.valueOf("SCT_${counter.type.name}")
                                        value = counter.value
                                        isBus = counter.isBus
                                        isTrolleybus = counter.isTrolleybus
                                        isTram = counter.isTram
                                        isMetro = counter.isMetro
                                    }
                                }.toList()
                            }
                        }.toList()
                    }

                    if (res.service.proTroika?.dict?.templates != null) {
                        troikaTemplate += res.service.proTroika?.dict?.templates!!.map { template ->
                            troikaTemplate {
                                id = template.id.toString()
                                appCode = template.appCode
                                crdCode = template.crdCode
                                name = template.name
                                type = AbonementType.valueOf(template.type.name)
                                limit = template.limit
                                prolongType = ProlongType.valueOf("PT_${template.prolongType.name}")
                                prolongStartDate = template.prolongStartDate.toTimestamp()
                                prolongEndDate = template.prolongEndDate.toTimestamp()
                                prolongDays = template.prolongDays
                                prolongCardCode = template.prolongCardCode ?: 0
                            }
                        }.toList()
                    }
                }
            }
        )
    }

    private fun toConstraint(c: ru.sbertroika.tkp3.manifest.model.pro.Constraint): Constraint = constraint {
        type = ConstraintType.valueOf(c.type.name)
        baseRule = ConstraintBaseRule.valueOf(c.baseRule.name)
        exception += c.exception.map { e ->
            constraintException {
                id = e.id.toString()
            }
        }.toList()
    }

    private suspend fun getTerminalManifest(terminalSerial: String): Either<Throwable, Triple<ru.sbertroika.tms.model.db.Terminal, TerminalOrganization, ru.sbertroika.tkp3.manifest.model.Manifest>> =
        Either.catch {
            val terminal = terminalRepository.findBySerialNumber(terminalSerial) ?: throw NotFound("Терминал $terminalSerial не зарегистрирован")
            val terminalOrganization = terminalToOrganizationRepository.findTerminalOrg(terminal.tId!!) ?: throw TerminalNotProject("Терминал $terminalSerial не привязан к проекту")

            manifestService.getManifestByProject(terminalOrganization.toProjectId!!.toString()).fold(
                {
                    throw ManifestNotFound("Отсутсвуют данные справочников")
                },
                {
                    if (it == null) throw ManifestNotFound("Отсутсвуют данные справочников")
                    Triple(terminal, terminalOrganization, it)
                }
            )
        }

    private suspend fun getManifest(manifest: Manifest): Either<Throwable, ru.sbertroika.tkp3.manifest.model.Manifest> =
        Either.catch {
            manifestService.getManifest(manifest.id, manifest.version).fold(
                {
                    throw ManifestNotFound("Отсутсвуют данные справочников")
                },
                {
                    if (it == null) throw ManifestNotFound("Отсутсвуют данные справочников")
                    if (it.service.pro == null) throw ManifestStateError("Отсутсвуют данные справочников")
                    it
                }
            )
        }
}