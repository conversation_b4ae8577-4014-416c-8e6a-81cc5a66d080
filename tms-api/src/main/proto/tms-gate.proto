syntax = "proto3";

package ru.sbertroika.tms.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";
import "common-tms.proto";
import "common-stop-list.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.tms.gate.v1";

service TMSGateService {
  // Проверка доступности сервера и получение мета-информации для синхронизации
  rpc heartbeat(HeartbeatRequest) returns (HeartbeatResponse);
  // Регистрация терминала
  rpc registration(RegistrationRequest) returns (RegistrationResponse);
  // Список пользователей с фильтрацией
  rpc terminalUserList(TerminalUserListRequest) returns (TerminalUserListResponse);
  // Отправка событий
  rpc eventsBatch(EventsBatchRequest) returns (EventsBatchResponse);

  // Получение манифеста
  rpc getManifest(google.protobuf.Empty) returns (ManifestResponse);
  // Запрос справочника станций
  rpc getStationList(Manifest) returns (StationListResponse);
  // Запрос справочника тарифов
  rpc getTariffList(Manifest) returns (TariffListResponse);
  // Запрос справочника маршрутов
  rpc getRouteList(Manifest) returns (RouteListResponse);
  // Запрос справочника продуктов
  rpc getProductList(Manifest) returns (ProductListResponse);
  // Запрос справочника Т/С
  rpc getTransportList(Manifest) returns (TransportListResponse);
  // Запрос билетного меню
  rpc getProductMenu(Manifest) returns (ProductMenuResponse);
  // Запрос обновления стоп-листа
  rpc getStopListUpdate(StopListUpdateRequest) returns (StopListUpdateResponse);
  // Запрос настроек для абонементов
  rpc getSubscriptionSettings(Manifest) returns (SubscriptionSettingsResponse);

  //
  rpc getSettingsList(google.protobuf.Empty) returns (TerminalSettingsListResponse);
}

message HeartbeatRequest {
  google.protobuf.Timestamp terminalTime = 1;   // Дата/время на терминале
  repeated string imei = 2;                     // IMEI[]
  uint32 charge = 3;                            // Заряд батареи
  common.v1.Position position = 4;              // Координаты устройства
  uint32 state = 5;                             // Статусы переферии (по 1 биту на устройство)
  string timeZone = 6;                          // TimeZone в которой работает терминал (Пример: +03.00)
  string timeZoneName = 7;                      // Имя TimeZone в которой работает терминал (Пример: Europe/Moscow)
}

message MetaInformation {
  google.protobuf.Timestamp serverTime = 1;     // Дата/время на сервере (в UTC+0)
  string timeZone = 2;                          // TimeZone в которой должен работать терминал (Пример: +03.00)
  string timeZoneName = 3;                      // Имя TimeZone в которой должен работать терминал (Пример: Europe/Moscow)
}

message HeartbeatResponse {
  oneof response {
    common.v1.OperationError error = 1;
    MetaInformation result = 2;
  }
}

message RegistrationRequest {
  string serialNumber = 1;          // Заводской номер терминала
  string csr = 2;                   // CSR в Base64
  repeated string imei = 3;         // IMEI[]
  string wifiMac = 4;               // MAC-адрес WiFi
  string bluetoothMac = 5;          // MAC-адрес bluetooth
  string ethernetMac = 6;           // MAC-адрес ethernet
  string versionPO = 7;             // Версия ПО терминала
}

message RegistrationResult {
  string certificate = 1;     // Сертификат в Base64
  uint32 index = 2;           // Порядковый номер терминала в проекте
}

message RegistrationResponse {
  oneof response {
    common.v1.OperationError error = 1;
    RegistrationResult result = 2;
  }
}

message TerminalUserListRequest{
  repeated common.v1.Filter filters = 1;
  optional common.v1.PaginationRequest pagination = 2;
}

message TerminalUserListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TerminalUserList result = 2;
  }
}

message TerminalUserList{
  optional common.v1.PaginationResponse pagination = 1;
  repeated TerminalUser user = 2;
}

message TerminalUser {
  string userId = 1;                  // идентификатор пользователя
  string login = 2;                   // логин
  string surname = 3;                 // Фамилия
  string name = 4;                    // Имя
  string middleName = 5;              // Отчество
  string personalNumber = 6;          // Табельный номер
  string password = 7;                 //Хэш пароля
  repeated string roles = 8;          // ролли
  repeated string groups = 9;         // группы
  bool enabled = 10;                  // активный
}

message Ticket {
  string ticketSeries = 1;                    // Серия билета в формате A-B-C-D
  string ticketNumber = 2;                    // Номер билета в формате A-B-C-N
  google.protobuf.Timestamp createdAt = 3;    // Дата/время формирования транзакции (в TimeZone в которой работает терминал)
  string manifest = 4;                        // Манифест
  uint32 manifestVersion = 5;                 // Версия манифеста
  uint32 shiftNumber = 6;                     // Номер смены
  string serviceId = 7;                       // Идентификатор сервиса (услуги или товара)
  string tariffId = 8;                        // Идентификатор тарифа
  string productId = 9;                       // Идентификатор продукта
  uint32 amount = 10;                         // Стоимость билета
  optional string stationFromId = 11;         // Станция входа
  optional string stationToId = 12;           // Станция выхода
}

message TerminalEvent {
  common.tms.EventType type = 1;                // Тип события
  google.protobuf.Timestamp createdAt = 2;      // Дата/время формирования события (в TimeZone в которой работает терминал)
  map<string, string> attributes = 3;           // Атрибуты события
}

message Batch {
  repeated TerminalEvent events = 1;    // термниальные собития
  repeated Ticket tickets = 2;          // Билеты
  string timeZone = 3;                  // TimeZone в которой работает терминал (Пример: +03.00)
  string timeZoneName = 4;              // Имя TimeZone в которой работает терминал (Пример: Europe/Moscow)
}

message EventsBatchRequest {
  Batch batch = 1;
  string hash = 2;
}

message EventsBatchResponse {
  oneof response {
    common.v1.OperationError error = 1;
    bool result = 2;
  }
}

message Manifest {
  message Organization {
    string id = 1;                    // Идентификатор организации
    string name = 2;                  // Полное наименование организации
    string inn = 3;                   // ИНН
    string address = 4;               // Адрес организации
    string paymentPlace = 5;          // Платежное место
  }
  string id = 1;
  uint32 version = 2;
  string sign = 3;
  google.protobuf.Timestamp validFrom = 4;
  google.protobuf.Timestamp validTill = 5;
  uint32 projectIndex = 6;
  Organization organization = 7;
}

message ManifestResponse {
  oneof response {
    common.v1.OperationError error = 1;
    Manifest manifest = 2;
  }
}

message ConstraintException {
  string id = 1;
}

message Constraint {
  common.v1.ConstraintType type = 1;
  common.v1.ConstraintBaseRule baseRule = 2;
  repeated ConstraintException exception = 3;
}

message Station {
  string id = 1;
  string name = 2;
  double lat = 3;
  double lon = 4;
}

message StationListResult {
  repeated Station station = 1;
}

message StationListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    StationListResult result = 2;
  }
}

message Tariff {
  string id = 1;
  string name = 2;
  repeated Constraint constraint = 3;
}

message TariffListResult {
  repeated Tariff tariff = 1;
}

message TariffListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TariffListResult result = 2;
  }
}

message RouteStation {
  string id = 1;
  uint32 pos = 2;
}

message Route {
  string id = 1;
  string name = 2;
  common.v1.RouteScheme scheme = 3;
  repeated RouteStation station = 4;
  repeated Constraint constraint = 5;
  uint32 routeIndex = 6;
}

message RouteListResult {
  repeated Route route = 1;
}

message RouteListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    RouteListResult result = 2;
  }
}

message Product {
  string id = 1;
  string name = 2;
}

message ProductListResult {
  repeated Product product = 1;
}

message ProductListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ProductListResult result = 2;
  }
}

message Transport {
  string id = 1;
  string number = 2;
  common.v1.TransportType type = 3;
  repeated Constraint constraint = 4;
}

message TransportListResult {
  repeated Transport transport = 1;
}

message TransportListResponse {
  oneof response {
    common.v1.OperationError error = 1;
    TransportListResult result = 2;
  }
}

message PriceRuleMatrixItem {
  string stationFrom = 1;
  string stationTo = 2;
  uint32 price = 3;
}

message PriceRule {
  enum TPaymentType {
    CASH = 0;
    EMV = 1;
    TROIKA_TICKET = 2;
    TROIKA_WALLET = 3;
    ABT_TICKET = 4;
    ABT_WALLET = 5;
    PROSTOR_TICKET = 6;
    QR_TICKET = 7;
    QR_WALLET = 8;
  }
  TPaymentType paymentType = 1;
  uint32 price = 2;
  repeated PriceRuleMatrixItem matrix = 3;
}

message ProductMenu {
  string productId = 1;
  string tariffId = 2;
  repeated PriceRule priceRules = 3;
}

message ProductMenuResult {
  repeated ProductMenu menu = 1;
}

message ProductMenuResponse {
  oneof response {
    common.v1.OperationError error = 1;
    ProductMenuResult result = 2;
  }
}

message StopListUpdateRequest {
  uint64 version = 1;                         // Текущая версия стоп-листа на терминале
  common.stop.list.StopListType type = 2;     // Тип стоп-листа
}

message StopListUpdateResult {
  repeated common.stop.list.StopListUpdate update = 1;   // Обновление
}

message StopListUpdateResponse {
  oneof response {
    common.v1.OperationError error = 1;
    StopListUpdateResult result = 2;
  }
}

message TerminalConnection {
  string ip_address = 1;
  uint32 port = 2;
  uint32 type = 3;
}

message TerminalSetting {
  string alias = 1;
  string value = 2;
}

message TerminalSettingResult {
  repeated TerminalSetting settings = 1;
  repeated TerminalConnection connections = 2;
}

message TerminalSettingsListResponse{
  oneof response {
    common.v1.OperationError error = 1;
    TerminalSettingResult result = 2;
  }
}

message SubscriptionSettingsResponse {
  oneof response {
    common.v1.OperationError error = 1;
    SubscriptionSettingsResult result = 2;
  }
}

message SubscriptionSettingsResult {
  repeated TroikaTemplate troikaTemplate = 1;
  repeated AbtTemplate abtTemplate = 2;
}

message AbtTemplate {
  // Идентификатор шаблона абонемента
  string id = 1;
  // Код приложения
  uint32 appCode = 2;
  // Код билета
  uint32 crdCode = 3;
  // Наименование абонемента
  string name = 4;
  // Тип абонемента
  common.v1.AbonementType type = 5;
  // Правила обработки предъявлений
  repeated AbtTemplatePassRule rules = 6;
  // Счетчики
  repeated AbtTemplateCounter counter = 7;
}

message AbtTemplatePassRule {
  // Порядковый номер предъявления
  uint32 index = 1;
  // Правило
  string action = 2;
}

message AbtTemplateCounter {
  // Тип счетчика
  common.v1.SubscriptionCounterType type = 1;
  // Базовое значение счетчика
  uint32 value = 2;
  // Доступность на транспорте
  bool isBus = 51;
  bool isTrolleybus = 52;
  bool isTram = 53;
  bool isMetro = 54;
}

message TroikaTemplate {
  // Идентификатор шаблона абонемента
  string id = 1;
  // Код приложения
  uint32 appCode = 2;
  // Код билета
  uint32 crdCode = 3;
  // Наименование абонемента
  string name = 4;
  // Тип абонемента
  common.v1.AbonementType type = 5;
  // Кол-во поездок которые будут записаны при активации
  uint32 limit = 6;
  // Тип активации продления
  common.v1.ProlongType prolongType = 7;
  // Дата и время начала действия абонемента
  google.protobuf.Timestamp prolongStartDate = 8;
  // Дата и время окончания действия абонемента
  google.protobuf.Timestamp prolongEndDate = 9;
  // Кол-во дней действия билета с начала даты активации
  uint32 prolongDays = 10;
  // Код продленного билета
  uint32 prolongCardCode = 11;
}