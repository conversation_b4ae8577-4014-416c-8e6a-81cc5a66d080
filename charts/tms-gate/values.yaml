# Default values for tms-gate.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: tms-gate

replicaCount: 1

image:
  repository: tms-gate
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

namespace: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  grpc:
    type: ClusterIP
    port: 5000
    targetPort: 5000
  grpc2:
    type: ClusterIP
    port: 5005
    targetPort: 5005


resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  profile: "all,log"
  client:
    logging:
      enable: true
  db:
    migration:
      enable: true
  keycloak:
    client_id: "test-auth"
    realm:
      url: "https://dev-auth.sbertroika.tech/realms/test-asop"
  kafka:
    servers: "**********:9092;***********:9092;**********:9092"
  zookeeper:
    nodes: "***********:2181,**********:2181,***********:2181"
  stop_list_service: "stop-list-gate.sl.svc.cluster.local:5000"

