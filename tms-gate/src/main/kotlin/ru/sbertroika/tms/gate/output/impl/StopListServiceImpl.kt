package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.common.stop.list.StopListUpdate
import ru.sbertroika.stop.list.gate.v1.StopListGateServiceGrpcKt
import ru.sbertroika.stop.list.gate.v1.stopListUpdateRequest
import ru.sbertroika.tms.gate.TerminalElement
import ru.sbertroika.tms.gate.model.StopListUpdateRequest
import ru.sbertroika.tms.gate.output.StopListService
import ru.sbertroika.tms.gate.output.repository.ProjectOrganizationRepo
import ru.sbertroika.tms.gate.output.repository.TerminalRepository
import ru.sbertroika.tms.gate.output.repository.TerminalToOrganizationRepository
import kotlin.coroutines.coroutineContext

@Service
class StopListServiceImpl(
    @Value("\${stop_list_service}")
    private val stopListServiceUrl: String,
    private val terminalRepo: TerminalRepository,
    private val terminalToOrganizationRepository: TerminalToOrganizationRepository,
    private val projectOrganizationRepo: ProjectOrganizationRepo
) : StopListService {

    private val channel = ManagedChannelBuilder.forTarget(stopListServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    private val client = StopListGateServiceGrpcKt.StopListGateServiceCoroutineStub(channel)

    override suspend fun getStopListUpdate(request: StopListUpdateRequest): Either<Throwable, StopListUpdate> = Either.catch {
        val serial = if (coroutineContext[TerminalElement] != null) coroutineContext[TerminalElement]?.serial else null
        if (serial != null) {
            val terminal = terminalRepo.findBySerialNumber(serial)
            if (terminal?.tId != null) {
                val terminalOrg = terminalToOrganizationRepository.findTerminalOrg(terminal.tId!!)
                if (terminalOrg != null) {
                    val response = client.getStopListUpdate(
                        stopListUpdateRequest {
                            projectId = terminalOrg.toProjectId.toString()
                            type = request.type
                            version = request.version
                        }
                    )
                    if (response.hasError()) throw Error(response.error.message)
                    response.result
                } else throw Error("Project not found for terminal $serial")
            } else throw Error("Terminal not found for serial: $serial")
        } else throw Error("Serial is null")
    }
}