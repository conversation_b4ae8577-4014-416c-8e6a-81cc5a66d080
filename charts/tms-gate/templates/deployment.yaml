apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "tms-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "tms-gate.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "tms-gate.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "tms-gate.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.security.egd=file:/dev/./urandom", "-jar", "tms-gate.jar"]
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path }}
              port: {{ .Values.readinessProbe.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path }}
              port: {{ .Values.livenessProbe.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
          {{- end }}
          env:
            - name: CLIENT_LOGGING_ENABLED
              value: {{ ternary "true" "false" .Values.env.client.logging.enable | quote }}
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: url
            - name: CLICKHOUSE_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: clickhouseUrl
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db
                  key: password
            - name: R2DB_URL
              valueFrom:
                secretKeyRef:
                  name: db
                  key: r2url
            - name: DB_MIGRATION_ENABLE
              value: {{ ternary "true" "false" .Values.env.db.migration.enable | quote }}
            - name: KAFKA_SERVERS
              value: {{ .Values.env.kafka.servers }}
            - name: ZOOKEEPER_NODES
              value: {{ .Values.env.zookeeper.nodes }}
            - name: KEYCLOAK_REALM_URL
              value: {{ .Values.env.keycloak.realm.url }}
            - name: KEYCLOAK_CLIENT_ID
              value: {{ .Values.env.keycloak.client_id }}
            - name: VAULT_HOST
              valueFrom:
                secretKeyRef:
                  name: vault
                  key: host
            - name: VAULT_ENGINE
              valueFrom:
                secretKeyRef:
                  name: vault
                  key: engine
            - name: VAULT_ROLE
              valueFrom:
                secretKeyRef:
                  name: vault
                  key: role
            - name: VAULT_CERT_DNS
              valueFrom:
                secretKeyRef:
                  name: vault
                  key: cert_dns
            - name: VAULT_TOKEN
              valueFrom:
                secretKeyRef:
                  name: vault
                  key: token
            - name: TMS_KEYCLOAK_HOST
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: host
            - name: TMS_KEYCLOAK_REALM
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: realm
            - name: TMS_KEYCLOAK_USER_REALM
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: user_realm
            - name: TMS_KEYCLOAK_SECRET
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: secret
            - name: TMS_KEYCLOAK_USERNAME
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: username
            - name: TMS_KEYCLOAK_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: password
            - name: TMS_KEYCLOAK_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak
                  key: client_id
            - name: SPRING_PROFILES_ACTIVE
              value: {{ .Values.env.profile }}
            - name: STOP_LIST_SERVICE
              value: {{ .Values.env.stop_list_service }}
            - name: S3_URL
              valueFrom:
                secretKeyRef:
                  name: tms
                  key: s3Url
            - name: S3_BUCKET
              valueFrom:
                secretKeyRef:
                  name: tms
                  key: s3Bucket
            - name: S3_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: tms
                  key: s3AccessKey
            - name: S3_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: tms
                  key: s3SecretKey
            - name: ZOOKEEPER_NAMESPACE
              valueFrom:
                secretKeyRef:
                  name: tms
                  key: zooNamespace
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: grpc
              containerPort: {{ .Values.service.grpc.targetPort }}
              protocol: TCP
            - name: grpc2
              containerPort: {{ .Values.service.grpc2.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}