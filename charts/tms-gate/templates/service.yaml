apiVersion: v1
kind: Service
metadata:
  name: {{ include "tms-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "tms-gate.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
    - name: grpc2
      port: {{ .Values.service.grpc2.port }}
      targetPort: {{ .Values.service.grpc2.targetPort }}
      protocol: TCP
  selector:
    {{- include "tms-gate.selectorLabels" . | nindent 4 }}