package ru.sbertroika.tms.gate.config

import org.jboss.resteasy.client.jaxrs.internal.ResteasyClientBuilderImpl
import org.keycloak.OAuth2Constants
import org.keycloak.admin.client.Keycloak
import org.keycloak.admin.client.KeycloakBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity
import org.springframework.security.config.web.server.ServerHttpSecurity
import org.springframework.security.web.server.SecurityWebFilterChain

@Configuration
@EnableWebFluxSecurity
open class KeycloakConfig(
    private val properties: KeycloakProperties
) {

    @Bean
    open fun keycloak(): Keycloak {
        return KeycloakBuilder.builder()
            .serverUrl(properties.host)
            .realm(properties.realm)
            .grantType(OAuth2Constants.PASSWORD)
            .username(properties.username)
            .password(properties.password)
            .clientId(properties.clientId)
            .clientSecret(properties.secret)
            .resteasyClient(
                ResteasyClientBuilderImpl()
                    .connectionPoolSize(10)
                    .build())
            .build()
    }

    @Bean
    open fun springSecurityFilterChain(http: ServerHttpSecurity): SecurityWebFilterChain {
        return http.authorizeExchange()
            .pathMatchers(
                "/actuator/**",
                "/v1/qr/**"
            ).permitAll()
            .anyExchange().authenticated()
            .and()
            .csrf()
            .disable()
            .build()
    }
}