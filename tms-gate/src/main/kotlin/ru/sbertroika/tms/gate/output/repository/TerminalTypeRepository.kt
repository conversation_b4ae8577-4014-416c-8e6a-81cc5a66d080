package ru.sbertroika.tms.gate.output.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Repository
import ru.sbertroika.tms.gate.output.model.db.TerminalType
import ru.sbertroika.tms.gate.output.model.db.TerminalTypePK
import ru.sbertroika.tms.gate.toInListSQLRequest
import ru.sbertroika.tms.gate.toLikeSQLRequest
import java.util.*


interface TerminalTypeCrudRepository : CoroutineCrudRepository<TerminalType, TerminalTypePK>

@Repository
open class TerminalTypeRepository(
    @Qualifier("mainDatabaseClient")
    private val dbClient: DatabaseClient,
    private val repository: TerminalTypeCrudRepository
) {
    companion object {
        const val query = "SELECT * FROM terminal_type_view as t"
    }

    suspend fun findById(id: String): TerminalType? {
        return dbClient.sql("$query WHERE t.ts_id = '$id'").map(::toTerminal).awaitOne()
    }

    suspend fun findWhereIdIn(ids: Set<String>): Flow<TerminalType> {
        return dbClient.sql("$query WHERE t.ts_id IN ${ids.toInListSQLRequest()}").map(::toTerminal).flow()
    }

    fun findAll(): Flow<TerminalType> {
        return dbClient.sql(query).map(::toTerminal).flow()
    }

    fun findAllByName(name: String): Flow<TerminalType> {
        return dbClient.sql("$query WHERE t.ts_name ILIKE ${name.toLikeSQLRequest()}").map(::toTerminal).flow()
    }

    suspend fun save(type: TerminalType) = repository.save(type)

    private fun toTerminal(t: io.r2dbc.spi.Readable) =
        TerminalType(
            tsId = t.get("ts_id") as UUID,
            tsName = t.get("ts_name") as String,
            tsSlug = t.get("ts_slug") as String,
            tsComment = t.get("ts_comment") as String,
            tsVersion = t.get("ts_version") as Int
        )
}