variables:
  TRIVY_VERSION: "0.49.1"
  TRIVY_CACHE_DIR: ".trivycache"
  TRIVY_FAIL_ON: "high"
  TRIVY_SEVERITY: "CRITICAL,HIGH"
  TRIVY_IGNORE_UNFIXED: "true"
  TRIVY_FORMAT: "template"
  TRIVY_TEMPLATE: "@contrib/gitlab.tpl"
  TRIVY_OUTPUT_PREFIX: "gl-trivy-report"
  ALLOW_TRIVY_FAILURE: "true"

.trivy-base: &trivy-base
  image:
    name: aquasec/trivy:${TRIVY_VERSION}
    entrypoint: [""]
#  cache:
#    key: "trivy-cache-${CI_JOB_NAME}"
#    paths:
#      - ${TRIVY_CACHE_DIR}
#  artifacts:
#    paths:
#      - ${TRIVY_CACHE_DIR}
#    expire_in: 1 week
#  allow_failure: ${ALLOW_TRIVY_FAILURE}
  rules:
    - if: $TRIVY_DISABLED
      when: never
    - if: $CI_COMMIT_BRANCH || $CI_COMMIT_TAG

#.trivy-scan-settings: &trivy-scan-settings
#  script:
#    - export TRIVY_OUTPUT="${TRIVY_OUTPUT_PREFIX}-${IMAGE_ALIAS}.json"
#    - trivy image
#      --exit-code ${TRIVY_FAIL_ON}
#      --severity ${TRIVY_SEVERITY}
#      --ignore-unfixed ${TRIVY_IGNORE_UNFIXED}
#      --cache-dir ${TRIVY_CACHE_DIR}
#      --format ${TRIVY_FORMAT}
#      --template ${TRIVY_TEMPLATE}
#      -o ${TRIVY_OUTPUT}
#      ${IMAGE_TO_SCAN}
#    - echo "Scan complete for ${IMAGE_TO_SCAN}"
#  artifacts:
#    reports:
#      container_scanning: ${TRIVY_OUTPUT_PREFIX}-${IMAGE_ALIAS}.json

#.trivy-scan-settings: &trivy-scan-settings
#  script:
#    - trivy image
#      --severity ${TRIVY_SEVERITY}
#      ${IMAGE_TO_SCAN}
#    - echo "Scan complete for ${IMAGE_TO_SCAN}"

.trivy-scan-settings: &trivy-scan-settings
  script:
    - apt-get install trivy
    - trivy image
      --severity ${TRIVY_SEVERITY}
      ${IMAGE_TO_SCAN}
    - echo "Scan complete for ${IMAGE_TO_SCAN}"

# Default scan job (backward compatible)
.trivy-container-scan:
  <<: *trivy-base
  stage: security
  tags:
    - docker
#    - agent2
  variables:
    IMAGE_ALIAS: "default"
    IMAGE_TO_SCAN: "${CI_REGISTRY_IMAGE}:${CI_COMMIT_SHA}"
  <<: *trivy-scan-settings
