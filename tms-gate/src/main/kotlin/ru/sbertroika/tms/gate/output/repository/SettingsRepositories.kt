package ru.sbertroika.tms.gate.output.repository

import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.data.repository.query.Param
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOne
import ru.sbertroika.tms.gate.output.model.db.*
import java.util.*

interface SettingsCrudRepository: CoroutineCrudRepository<Settings, SettingsPK> {
    override fun findAllById(ids: Flow<SettingsPK>): Flow<Settings>

    @Query(value =  "select * from settings o\n" +
            "INNER JOIN (\n" +
            "    SELECT s_id, MAX(s_version) vers\n" +
            "    FROM settings\n" +
            "    GROUP BY s_id\n" +
            ") o2 ON o.s_id = o2.s_id AND o.s_version = o2.vers\n" +
            " AND o.s_id IN :listId"
    )
    fun findAllSettingsIds(@Param("listId") listId: List<UUID?>): Flow<Settings>
}

interface TerminalSettingsViewRepository: CoroutineCrudRepository<TerminalSettingsView, UUID> {
    fun findAllByProfileId(profileId: UUID): Flow<TerminalSettingsView>
}

interface SettingsGroupCrudRepository: CoroutineCrudRepository<SettingsGroup, SettingsGroupPK> {

    @Query(value =  "select * from settings_group o\n" +
            "INNER JOIN (\n" +
            "    SELECT sg_id, MAX(sg_version) vers\n" +
            "    FROM settings_group\n" +
            "    GROUP BY sg_id\n" +
            ") o2 ON o.sg_id = o2.sg_id AND o.sg_version = o2.vers\n" +
            " AND o.sg_id IN :listId"
    )
    fun findAllBySgId(@Param("listId") listId: List<UUID?>): Flow<SettingsGroup>
}
interface TerminalProfileCrudRepository: CoroutineCrudRepository<TerminalProfile, TerminalProfilePK>
interface TemplateSettingsCrudRepository: CoroutineCrudRepository<TemplateSettings, TemplateSettingsPK> {
    @Query(value =  "select * from template_settings o\n" +
            "INNER JOIN (\n" +
            "    SELECT ts_id, MAX(ts_version) vers\n" +
            "    FROM template_settings\n" +
            "    GROUP BY ts_id\n" +
            ") o2 ON o.ts_id = o2.ts_id AND o.ts_version = o2.vers\n" +
            " AND o.ts_id IN :listId"
    )
    fun findAllByTemplateSettingsId(@Param("listId") listId: List<UUID?>): Flow<TemplateSettings>
}
interface SettingsGroupToTerminalProfileRepository: CoroutineCrudRepository<SettingsGroupToTerminalProfile, SettingsGroupToTerminalProfilePK> {


    @Query(value = "select * from settings_group__terminal_profile o\n" +
            "INNER JOIN (\n" +
            "    SELECT sgtp_id, MAX(sgtp_version) vers\n" +
            "    FROM settings_group__terminal_profile\n" +
            "    GROUP BY sgtp_id\n" +
            ") o2 ON o.sgtp_id = o2.sgtp_id AND o.sgtp_version = o2.vers\n" +
            " AND o.tp_id = :tpId")
    fun getAllByTpIdForAssign(@Param("tpId")tpId: UUID): Flow<SettingsGroupToTerminalProfile>

    @Query(value = "select * from settings_group__terminal_profile o\n" +
            "INNER JOIN (\n" +
            "    SELECT sgtp_id, MAX(sgtp_version) vers\n" +
            "    FROM settings_group__terminal_profile\n" +
            "    GROUP BY sgtp_id\n" +
            ") o2 ON o.sgtp_id = o2.sgtp_id AND o.sgtp_version = o2.vers\n" +
            " AND o.tp_id = :tpId and o.sgtp_actual_till is null")
    fun getAllByTpId(tpId: UUID): Flow<SettingsGroupToTerminalProfile>
}
interface SettingsToTerminalProfileRepository: CoroutineCrudRepository<SettingsToTerminalProfile, SettingsToTerminalProfilePK> {
    @Query(value = "select * from settings__terminal_profile o\n" +
            "INNER JOIN (\n" +
            "    SELECT stp_id, MAX(stp_version) vers\n" +
            "    FROM settings__terminal_profile\n" +
            "    GROUP BY stp_id\n" +
            ") o2 ON o.stp_id = o2.stp_id AND o.stp_version = o2.vers\n" +
            " AND o.tp_id = :tpId and o.stp_actual_till is null ")
    fun getAllByTpId(@Param("tpId")tpId: UUID): Flow<SettingsToTerminalProfile>

    @Query(value = "select * from settings__terminal_profile o\n" +
            "INNER JOIN (\n" +
            "    SELECT stp_id, MAX(stp_version) vers\n" +
            "    FROM settings__terminal_profile\n" +
            "    GROUP BY stp_id\n" +
            ") o2 ON o.stp_id = o2.stp_id AND o.stp_version = o2.vers\n" +
            " AND o.tp_id = :tpId")
    fun getAllByTpIdForAssign(@Param("tpId")tpId: UUID): Flow<SettingsToTerminalProfile>
}
interface TemplateSettingsToSettingsGroupRepository: CoroutineCrudRepository<TemplateSettingsToSettingsGroup, TemplateSettingsToSettingsGroupPK> {

    @Query(value = "select * from template_settings__settings_group o\n" +
            "INNER JOIN (\n" +
            "    SELECT tssg_id, MAX(tssg_version) vers\n" +
            "    FROM template_settings__settings_group\n" +
            "    GROUP BY tssg_id\n" +
            ") o2 ON o.tssg_id = o2.tssg_id AND o.tssg_version = o2.vers\n" +
            " AND o.sg_id = :sgId and o.tssg_actual_till is null")
    fun getAllBySgId(@Param("sgId") sgId: UUID): Flow<TemplateSettingsToSettingsGroup>

    @Query(value = "select * from template_settings__settings_group o\n" +
            "INNER JOIN (\n" +
            "    SELECT tssg_id, MAX(tssg_version) vers\n" +
            "    FROM template_settings__settings_group\n" +
            "    GROUP BY tssg_id\n" +
            ") o2 ON o.tssg_id = o2.tssg_id AND o.tssg_version = o2.vers\n" +
            " AND o.sg_id = :sgId")
    fun getAllBySgIdForAssign(@Param("sgId") sgId: UUID): Flow<TemplateSettingsToSettingsGroup>

}

abstract class AbstractRepository<E, K>(
    open val dbClient: DatabaseClient,
    open val repository: CoroutineCrudRepository<E, K>
) {
    abstract fun getQuery(isCount: Boolean = false): String
    abstract fun toEntity(t: Readable): E
    protected fun getPageRequest(page: Int, limit: Int): String = "${getQuery()} OFFSET ${page * limit} LIMIT $limit"
    abstract suspend fun findById(id: String): E?
    abstract fun findAll(page: Int, limit: Int): Flow<E>
    abstract fun findAll(): Flow<E>
    suspend fun countAll(): Int {
        return dbClient.sql(getQuery(true)).map {
                t -> (t.get(0) as Long).toInt()
        }.awaitOne()
    }

    abstract suspend fun deleted(id: String, userId: UUID)

    suspend fun save(entity: E) = repository.save(entity)
}

