version: '3.7'

services:
  tms_gate:
    image: tms-gate:local
    container_name: tms-gate
    ports:
      - 5010:5000
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      DB_URL: postgresql://tms-db:5432/tms
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_MIGRATION_ENABLE: "true"
      R2DB_URL: ******************************************/tms
      ZOOKEEPER_NODES: zoo1:2181
      KAFKA_SERVERS: kafka:29092
    depends_on:
      - tms_db
      - kafka
    networks:
      - tms-domain_default

  tms_db:
    image: postgres:14
    container_name: tms-db
    restart: always
    ports:
      - 5432:5432
    volumes:
      - ./data/tms:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: tms
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - tms-domain_default

  tms_processing:
    image: tms-processing:local
    container_name: tms-processing
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - tms_db
      - kafka
    environment:
      DB_URL: postgresql://tms-db:5432/tms
      DB_USER: postgres
      DB_PASSWORD: postgres
      R2DB_URL: ******************************************/tms
      ZOOKEEPER_NODES: zoo1:2181
      S3_URL: http://s3:9001
      S3_ACCESS_KEY_ID: s3__user
      S3_SECRET_ACCESS_KEY: s3__pass
      S3_BUCKET: tkp3-manifest
      KAFKA_SERVERS: kafka:29092
    networks:
      - tms-domain_default

  keycloak_db:
    image: docker.io/bitnami/postgresql:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - POSTGRESQL_USERNAME=bn_keycloak
      - POSTGRESQL_DATABASE=bitnami_keycloak
    networks:
      - keycloak_net

  keycloak:
    image: docker.io/bitnami/keycloak:latest
    container_name: keycloak
    ports:
      - "8080:8080"
    environment:
      - KEYCLOAK_CREATE_ADMIN_USER=true
      - KEYCLOAK_ADMIN_USER=user
      - KEYCLOAK_ADMIN_PASSWORD=12345
    depends_on:
      - keycloak_db
    networks:
      - keycloak_net

  zoo1:
    image: zookeeper
    restart: always
    hostname: zoo1
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zoo1:2888:3888;2181
    networks:
      - tms-domain_default

  s3:
    image: minio/minio:RELEASE.2025-05-24T17-08-30Z
    container_name: s3
    environment:
      MINIO_ROOT_USER: s3__user
      MINIO_ROOT_PASSWORD: s3__pass
      MINIO_DOMAIN: s3 # переключение в режим virtual-hosts-style
    ports:
      - 9001:9001 # WebUI
    volumes:
      - ./data/minio:/data
    command: server --console-address ":9001" /data
    networks:
      - tms-domain_default

  kafka:
    image: confluentinc/cp-kafka:7.5.9
    container_name: kafka
    ports:
      - 29092:29092
    depends_on:
      - zoo1
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zoo1:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - tms-domain_default

  kafka-ui:
    image: provectuslabs/kafka-ui:v0.7.2
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - 8086:8080
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zoo1:2181
    networks:
      - tms-domain_default

networks:
  tms-domain_default:
    driver: bridge
  keycloak_net:
    driver: bridge