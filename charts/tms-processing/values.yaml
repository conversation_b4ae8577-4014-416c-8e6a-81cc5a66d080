# Default values for tms-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: tms-processing

replicaCount: 1

image:
  repository: tms-processing
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

namespace: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}
#  sidecar.istio.io/rewriteAppHTTPProbers: "false"

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  grpc:
    type: ClusterIP
    port: 5000
    targetPort: 5000

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  client:
    logging:
      enable: true
  kafka:
    servers: "**********:9092;***********:9092;**********:9092"
  zookeeper:
    nodes: "***********:2181,**********:2181,***********:2181"
