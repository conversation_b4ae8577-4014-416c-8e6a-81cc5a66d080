package ru.sbertroika.tms.gate.output.impl

import arrow.core.Either
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.keycloak.representations.idm.GroupRepresentation
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.common.NotFound
import ru.sbertroika.tms.gate.output.KeycloakService
import ru.sbertroika.tms.gate.output.repository.TerminalRepository
import ru.sbertroika.tms.gate.output.repository.TerminalToOrganizationRepository
import ru.sbertroika.tms.gate.output.repository.TerminalUserRepository
import ru.sbertroika.tms.gate.v1.TerminalUser
import ru.sbertroika.tms.gate.v1.TerminalUserList
import ru.sbertroika.tms.gate.v1.TerminalUserListRequest
import ru.sbertroika.tms.gate.v1.TerminalUserListResponse
import java.util.stream.Collectors


@Service
class KeycloakServiceImpl(
    private val terminalRepo: TerminalRepository,
    private val terminalToOrganizationRepository: TerminalToOrganizationRepository,
    private val terminalUserRepository: TerminalUserRepository
) : KeycloakService {

    private val logger = LoggerFactory.getLogger(this.javaClass.name)

    override suspend fun terminalUserList(
        request: TerminalUserListRequest,
        isTerminal: Boolean,
        serial: String?
    ): Either<Error, TerminalUserListResponse> {
        return try {
            if (isTerminal) {
                if (serial != null) {
                    val terminal = terminalRepo.findBySerialNumber(serial)
                    if (terminal != null) {
                        val terminalToOrganization = terminalToOrganizationRepository.findTerminalOrg(terminal.tId!!)
                        if (terminalToOrganization != null) {
                            val result =
                                terminalUserRepository.findByOrganisationId(terminalToOrganization.toOrganizationId!!)
                            val users = result
                                .filter { !it.pinHash.isNullOrEmpty() }
                                .filter { it.enabled }
                                .map { mapToTerminalUser(it) }
                                .toList()
                            Either.Right(
                                TerminalUserListResponse.newBuilder().setResult(
                                    TerminalUserList.newBuilder().addAllUser(users)
                                ).build()
                            )
                        } else
                            Either.Left(NotFound("Не найдена группа для терминала (${terminal.tId!!})"))
                    } else
                        Either.Left(NotFound("Терминал $serial не зарегистрирован в TMS"))
                } else
                    Either.Left(NotFound("Номер терминала null"))
            } else {
                val result = terminalUserRepository.findAll().toList().toMutableList()
                logger.info(
                    "TERMINAL_USER_LIST",
                    "list size: ${result.size},ul:${
                        result.stream().map { "userid:${it?.profileId}" }.collect(Collectors.joining(","))
                    }"
                )
                Either.Right(
                    TerminalUserListResponse.newBuilder().setResult(
                        TerminalUserList
                            .newBuilder()
                            .addAllUser(result.stream().map { mapToTerminalUser(it!!) }.toList())
                    ).build()
                )
            }
        } catch (e: Exception) {
            Either.Left(Error(e))
        }
    }

    private fun mapGroup(groups: List<GroupRepresentation>, prefix: String? = null): MutableList<String> {
        val result: MutableList<String> = mutableListOf()
        for (group in groups) {
            val groupName = if (prefix.isNullOrEmpty())
                group.name
            else
                "$prefix/${group.name}"
            result.add(groupName)
            if (!group.subGroups.isNullOrEmpty())
                result.addAll(mapGroup(group.subGroups, groupName))
        }
        return result
    }

    private fun mapToTerminalUser(user: ru.sbertroika.tms.gate.output.model.db.TerminalUser): TerminalUser {
        logger.info("MAP USER", user.profileId.toString())
        return TerminalUser
            .newBuilder()
            .setUserId(user.profileId.toString())
            .setLogin(user.login)
            .setName(user.name)
            .setSurname(user.surname)
            .addAllGroups(arrayListOf(user.organizationId.toString()))
            .addAllRoles(arrayListOf(user.role))
            .setMiddleName(if (user.middleName.isNullOrEmpty()) "" else user.middleName)
            .setPassword(user.pinHash)
            .setPersonalNumber(if (user.personalNumber.isNullOrEmpty()) "" else user.personalNumber)
            .setEnabled(user.enabled)
            .build()
    }
}