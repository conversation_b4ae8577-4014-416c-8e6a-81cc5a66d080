package ru.sbertroika.tms.gate.util

import arrow.core.Either
import org.lognet.springboot.grpc.security.GrpcSecurity
import org.springframework.security.core.Authentication
import org.springframework.security.oauth2.jwt.Jwt
import ru.sbertroika.tms.gate.config.AuthenticationError

fun validateUser(): Either<Error, Authentication> {
    val auth: Authentication = GrpcSecurity.AUTHENTICATION_CONTEXT_KEY.get()
    return if (auth.isAuthenticated) {
        Either.Right(auth)
    } else {
        Either.Left(AuthenticationError())
    }
}

fun Authentication.userId() = (principal as Jwt).claims["sub"] as String