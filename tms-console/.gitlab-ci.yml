include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/main-pipeline-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# TMS-CONSOLE: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
tms_console_build_develop:
  stage: build
  variables:
    SERVICE_NAME: "tms-console"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-console/**
        - ./*

tms_console_helm_kubeval_testing_develop:
  stage: test
  needs: [tms_console_build_develop]
  variables:
    SERVICE_NAME: "tms-console"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-console/**
        - charts/tms-console/**

tms_console_deploy_chart_develop:
  stage: deploy
  needs:
    - tms_console_helm_kubeval_testing_develop
    - job: tms_console_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "tms-console"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - tms-console/**
        - charts/tms-console/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - tms-console/**

tms_console_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "tms-console"
  extends:
    - .docker_build_and_push
  <<: *tag_rules

tms_console_helm_kubeval_testing_tag:
  stage: test
  needs:
    - tms_console_build_tag
  variables:
    SERVICE_NAME: "tms-console"
  extends:
    - .validate_helm_template
  <<: *tag_rules

tms_console_deploy_chart_tag:
  stage: deploy
  needs:
    - tms_console_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "tms-console"
  extends:
    - .deploy_helm_template
  <<: *tag_rules
