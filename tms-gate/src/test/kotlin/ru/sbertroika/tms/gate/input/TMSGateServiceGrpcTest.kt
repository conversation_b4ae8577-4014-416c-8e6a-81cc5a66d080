package ru.sbertroika.tms.gate.input

import com.google.protobuf.Empty
import com.google.protobuf.Timestamp
import io.grpc.*
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.common.stop.list.StopListType
import ru.sbertroika.common.v1.Position
import ru.sbertroika.tms.gate.v1.*
import java.time.Instant
import java.time.ZoneId

class TMSGateServiceGrpcTest {

    companion object {
        //private const val server = "pki.tms.sbertroika.tech"
        private const val server = "localhost"
        private const val port = 5010
        private const val isTls = false

        private val manifestId = "43db9de7-72ec-4eae-8978-8aef1c46873a-2024-01-19"
        private val manifestVersion = 1
    }

    @Test
    fun heartbeatTest(): Unit = runBlocking {
        val request = HeartbeatRequest.newBuilder()
            .setCharge(40)
            .setPosition(
                Position.newBuilder()
                    .setLatitude(0.00)
                    .setLongitude(0.00)
            )
            .setTerminalTime(
                Timestamp.newBuilder()
                    .setSeconds(System.currentTimeMillis() / 1000)
                    .setNanos(System.nanoTime().toInt())
            )
            .setTimeZone("+03:00")
            .setTimeZoneName("Europe/Moscow")
            .addImei("000000000000000000")
            .build()
        val response = client().heartbeat(request, terminalMeta())
        println("response: $response")
        println(
            Instant.ofEpochSecond(response.result.serverTime.seconds, response.result.serverTime.nanos.toLong())
                .atZone(ZoneId.of(request.timeZoneName))
                .toLocalDateTime()
        )
        assertFalse(response.hasError())
    }

    @Test
    fun terminalUserListTest(): Unit = runBlocking {
        val response =  client().terminalUserList(
            TerminalUserListRequest
                .newBuilder()
                .build(),
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun registerTerminal() = runBlocking {
        val req = registrationRequest {
            serialNumber = "11224455"
            csr =
                "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"
            wifiMac = "wifi"
            bluetoothMac = "bluet"
            ethernetMac = "ethernet"
            versionPO = "vers po"
        }
        val resp = client().registration(req)
        println("response: $resp")
        assertFalse(resp.hasError())
    }

    @Test
    fun getManifestTest(): Unit = runBlocking {
        val response = client().getManifest(
            Empty.getDefaultInstance(),
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getStationListTest(): Unit = runBlocking {
        val response = client().getStationList(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getProductListTest(): Unit = runBlocking {
        val response = client().getProductList(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getTariffListTest(): Unit = runBlocking {
        val response = client().getTariffList(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getRouteListTest(): Unit = runBlocking {
        val response = client().getRouteList(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getTransportListTest(): Unit = runBlocking {
        val response = client().getTransportList(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getProductMenuTest(): Unit = runBlocking {
        val response = client().getProductMenu(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getSubscriptionSettingsTest(): Unit = runBlocking {
        val response = client().getSubscriptionSettings(
            manifest {
                id = manifestId
                version = manifestVersion
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getStopListUpdateTest(): Unit = runBlocking {
        val response = client().getStopListUpdate(
            stopListUpdateRequest {
                version = 0
                type = StopListType.SL_EMV
            },
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    @Test
    fun getSettingsListTest(): Unit = runBlocking {
        val response = client().getSettingsList(
            Empty.newBuilder().build(),
            terminalMeta()
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    private fun terminalMeta(): Metadata {
        val meta = Metadata()
        meta.put(Metadata.Key.of("tms.terminal.serial", Metadata.ASCII_STRING_MARSHALLER), "1003352070040230")
        return meta
    }

    private fun client(): TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub {
        return if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            val channel: ManagedChannel = Grpc.newChannelBuilderForAddress(server, port, credentials)
                .build()
            return TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub(channel)
        } else {
            val channel = ManagedChannelBuilder.forTarget("$server:$port")
                .usePlaintext()
                .build()
            TMSGateServiceGrpcKt.TMSGateServiceCoroutineStub(channel)
        }
    }
}